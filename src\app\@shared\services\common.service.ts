import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import * as _moment from 'moment/moment';
import { BehaviorSubject, Observable } from 'rxjs';
import { ApiUrl } from '../constants';
import { ChunkUploadProgressDetails } from '../models/share';
import { StorageService } from './storage.service';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Injectable({
  providedIn: 'root'
})
export class CommonService {
  autoLogout = new BehaviorSubject<boolean>(true);
  isChunkUploadInProgress$ = new BehaviorSubject<boolean>(false);
  commonUploadFinish$ = new BehaviorSubject<boolean>(false);
  chunkUploadDetails$ = new BehaviorSubject<ChunkUploadProgressDetails[]>(null);
  commonChunkUploadDetails: ChunkUploadProgressDetails[] = [];
  remainingChunks$ = new BehaviorSubject<number>(0);
  totalChunksCount = 0;
  moment = (_moment as any).default ? (_moment as any).default : _moment;

  constructor(private readonly http: HttpClient, private readonly storageService: StorageService, private sanitizer: DomSanitizer) {}

  // To convert string latters for sorting without Case Sensitive
  compareInsensitive = (direction: any, a: any, b: any) => {
    // Converting strings to lowercase
    const first = typeof a === 'string' ? a.toLowerCase() : a;
    const second = typeof b === 'string' ? b.toLowerCase() : b;

    if (first < second) {
      return -1 * direction;
    }
    if (first > second) {
      return direction;
    }
    return 0;
  };

  // To convert Date formate in MM/DD/YYYY HH:mm
  convertDateFormat = value => {
    if (!value) {
      return '';
    }
    return this.moment(value).format('MM/DD/YYYY HH:mm');
  };

  // get year
  getYear(withSelect = true, withIdName = false, tillCurrentYear = false, minYear: number = null) {
    const year = new Date().getFullYear();
    const range = [];
    if (withSelect) {
      range.push('Select');
    }
    if (tillCurrentYear) {
      for (let i = 2020; i <= year; i++) {
        range.push({ id: i, name: i });
      }
    } else {
      const startYear = minYear ? minYear : year - 2;
      if (withIdName) {
        for (let i = startYear; i <= startYear + 50; i++) {
          range.push({ id: i, name: i });
        }
      } else {
        for (let i = startYear; i <= startYear + 50; i++) {
          range.push(i);
        }
      }
    }
    return range;
  }

  getCurrentYear(): number {
    const month = new Date().getMonth() + 1;
    const year = new Date().getFullYear();
    if (month > 1) {
      return year;
    } else {
      return year - 1;
    }
  }

  // Common Create PDF Object
  createObject(data, type) {
    const blob = new Blob([data], { type: type });
    const downloadURL = window.URL.createObjectURL(data);
    const link = document.createElement('a');
    link.href = downloadURL;
    return link;
  }

  // Create ReportName
  CreateReportName(siteName, workorderName) {
    return `${siteName}_${workorderName}`;
  }

  exportExcel(rows, tittle) {
    let csvContent = '';
    /* add the column delimiter as comma(,) and each row splitted by new line character (\n) */
    rows.forEach(function (rowArray) {
      let row = '';
      for (const i of rowArray) {
        if (i) {
          let val: string = i.toString();
          val = val.replace(/\r?\n|\r/g, ' ').toString();
          if (val.startsWith('"=HYPERLINK(')) {
            row += val;
            continue;
          }
          if (val.includes('°')) {
            val = val.replace('°', '').toString();
            val = `=${val}&CHAR(176)`;
          }
          if (val.includes('º')) {
            val = val.replace('º', '').toString();
            val = `=${val}&CHAR(176)`;
          }
          if (!val.includes('="')) {
            val = val.split('"').join(`'`).toString();
          }
          if (val.length < 2700) {
            val = val.split('<br/>').join('\n').toString();
            val = val.split('<br />').join('\n').toString();
          } else {
            val = val.split('<br/>').join('').toString();
            val = val.split('<br />').join('').toString();
          }
          row += !val.includes('="') ? `"${val}",` : `${val},`;
        } else {
          row += `"${i}",`;
        }
      }
      csvContent += `${row}\r\n`;
    });

    /* create a hidden <a> DOM node and set its download attribute */
    const a = document.createElement('a');
    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8' });
    const url = window.URL.createObjectURL(blob);
    a.href = url;
    a.download = `${tittle}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
    a.remove();
  }

  setAutoLogoutValue(value: boolean) {
    this.storageService.set('autoLogout', value);
    this.autoLogout.next(value);
  }

  getAutoLogoutValue(): Observable<boolean> {
    return this.autoLogout.asObservable();
  }

  encryptUserId() {
    return this.http.get(`${ApiUrl.Encrypt_UserId}${this.storageService.get('userID')}`, { responseType: 'text' });
  }

  uploadChunk(formData: FormData, chunkDetails?: ChunkUploadProgressDetails): Observable<any> {
    if (chunkDetails) {
      const index = this.commonChunkUploadDetails.findIndex(details => details.fileUploadTimeStamp === chunkDetails.fileUploadTimeStamp);
      // Only update or add if there is a real change (avoids redundant pushes)
      if (index !== -1) {
        this.commonChunkUploadDetails[index] = { ...this.commonChunkUploadDetails[index], ...chunkDetails };
      } else {
        this.commonChunkUploadDetails.push({ ...chunkDetails }); // Spread operator to avoid object references
      }
      // Emit a deep clone to ensure subscribers detect changes
      this.chunkUploadDetails$.next([...this.commonChunkUploadDetails]);
    }

    return this.http.post<any>(ApiUrl.UPLOAD_FILES_TO_GALLERY, formData);
  }

  sanitizeHtml(content: string): SafeHtml {
    return this.sanitizer.bypassSecurityTrustHtml(content);
  }

  removeHtmlTagsForToolTips(content: string): string {
    return content.replace(/<br\/?>/g, ',\n').replace(/<\/?b>/g, '');
  }

  formatNumber(value: number | string, minFractionDigits = 2, maxFractionDigits = 2): string {
    const numberValue = Number(value);
    if (isNaN(numberValue)) return '';

    return numberValue.toLocaleString('en-US', {
      minimumFractionDigits: minFractionDigits,
      maximumFractionDigits: maxFractionDigits
    });
  }

  createObjectFromBased64(base64String: string, contentType = 'application/octet-stream') {
    const byteCharacters = atob(base64String);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: contentType });
  }

  isValidEmail(email: string): boolean {
    const trimmedEmail = email.trim();
    if (!trimmedEmail) return false;
    const emailRegex = new RegExp('^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,4}$');
    return emailRegex.test(trimmedEmail);
  }

  checkKeysInObject(
    keys: string[],
    obj: Record<string, any>
  ): {
    allExist: boolean;
    atLeastOneExists: boolean;
    existing: string[];
    missing: string[];
  } {
    const existing = keys.filter(k => k in obj);
    return {
      allExist: keys.every(k => k in obj),
      atLeastOneExists: existing.length > 0,
      existing,
      missing: keys.filter(k => !(k in obj))
    };
  }
}
