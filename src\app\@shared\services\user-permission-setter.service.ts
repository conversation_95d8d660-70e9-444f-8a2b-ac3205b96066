import { Injectable } from '@angular/core';
import { AppConstants } from '../constants';
import {
  AUTHORITY_ROLE_STRING,
  QE_MENU_MODULE_ENUM,
  QE_MENU_MODULE_OLD_USER_PERMISSION_LIST,
  QE_MENU_MODULE_USER_PERMISSION_ENUM,
  ROLE_TYPE
} from '../enums';
import { CommonService } from './common.service';
import { StorageService } from './storage.service';

@Injectable({
  providedIn: 'root'
})
export class UserPermissionSetterService {
  constructor(private readonly storageService: StorageService, private readonly commonService: CommonService) {}

  checkOldPermissionKeyAndReplace(): void {
    const userPermission = this.storageService.get(AppConstants.userPermissions);
    const oldUserPermission = this.storageService.get('permission'); // Old key used previously
    const { allExist, atLeastOneExists, existing, missing } = this.commonService.checkKeysInObject(
      QE_MENU_MODULE_OLD_USER_PERMISSION_LIST,
      userPermission || oldUserPermission
    );

    if (allExist || atLeastOneExists || oldUserPermission) {
      this.setPermission();
    }
  }

  setPermission(): void {
    const token = this.storageService.get(AppConstants.authenticationToken),
      tokenData = token.split('.')[1],
      decodedTokenJSONData = window.atob(tokenData),
      decodedTokenData = JSON.parse(decodedTokenJSONData);

    this.storageService.set('userID', decodedTokenData.UserID);
    switch (decodedTokenData.role) {
      case AUTHORITY_ROLE_STRING[ROLE_TYPE.ADMIN]:
        this.addAdminPermission();
        break;
      case AUTHORITY_ROLE_STRING[ROLE_TYPE.PORTFOLIOMANAGER]:
        this.addPortfolioManagerPermission();
        break;
      case AUTHORITY_ROLE_STRING[ROLE_TYPE.MANAGER]:
        this.addManagerPermission();
        break;
      case AUTHORITY_ROLE_STRING[ROLE_TYPE.FIELDTECH]:
        this.addFieldTechPermission();
        break;
      case AUTHORITY_ROLE_STRING[ROLE_TYPE.CUSTOMER]:
        this.addCustomerPermission();
        break;
      case AUTHORITY_ROLE_STRING[ROLE_TYPE.ANALYST]:
        this.addAnalystPermission();
        break;
      case AUTHORITY_ROLE_STRING[ROLE_TYPE.SUPPORT]:
        this.addSupportPermission();
        break;
      case AUTHORITY_ROLE_STRING[ROLE_TYPE.DIRECTOR]:
        this.addDirectorPermission();
        break;
    }
  }

  addAdminPermission(): void {
    const adminPermission = {
      // Site Info
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SITE_INFO]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_CUSTOMERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_PORTFOLIOS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_SITES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DEVICES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_EQUIPMENT]]: true,

      // Preventive Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SCOPE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_WORK_ORDERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SITE_AUDIT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE]]: true,

      // Corrective Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_ALL_TICKETS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_BILLING_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_MAP_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_RMA_REPORT]]: true,

      // Availability
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVAILABILITY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DATA_TABLE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS]]: true,

      // Performance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PERFORMANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CHARTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CARDS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DATA_TABLE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_ALERTS]]: true,

      // Safety
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SAFETY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_NOTIFICATIONS]]: true,
      // Safety Settings
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SETTINGS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_HAZARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_BARRIER]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_LOTO]]: true,

      // Operations
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OPERATIONS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REGION_MAPPING]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_SERVICES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CONTRACTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS]]: true,

      // Admin
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.ADMIN]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_USERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_EMAIL_LOG]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_ANALYTICS]]: true
    };
    this.storageService.set(AppConstants.userPermissions, adminPermission);
  }

  addPortfolioManagerPermission(): void {
    const portfolioManagerPermission = {
      // Site Info
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SITE_INFO]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_CUSTOMERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_PORTFOLIOS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_SITES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DEVICES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_EQUIPMENT]]: true,

      // Preventive Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SCOPE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_WORK_ORDERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SITE_AUDIT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE]]: false,

      // Corrective Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_ALL_TICKETS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_BILLING_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_MAP_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_RMA_REPORT]]: true,

      // Availability
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVAILABILITY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DATA_TABLE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS]]: true,

      // Performance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PERFORMANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CHARTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CARDS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DATA_TABLE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_ALERTS]]: true,

      // Safety
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SAFETY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_NOTIFICATIONS]]: true,
      // Safety Settings
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SETTINGS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_HAZARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_BARRIER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_LOTO]]: false,

      // Operations
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OPERATIONS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REPORTS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REGION_MAPPING]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_SERVICES]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CONTRACTS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS]]: false,

      // Admin
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.ADMIN]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_USERS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_EMAIL_LOG]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_ANALYTICS]]: false
    };

    this.storageService.set(AppConstants.userPermissions, portfolioManagerPermission);
  }

  addAnalystPermission(): void {
    const analystPermission = {
      // Site Info
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SITE_INFO]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_CUSTOMERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_PORTFOLIOS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_SITES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DEVICES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_EQUIPMENT]]: true,

      // Preventive Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SCOPE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_WORK_ORDERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SITE_AUDIT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE]]: false,

      // Corrective Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_ALL_TICKETS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_BILLING_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_MAP_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_RMA_REPORT]]: true,

      // Availability
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVAILABILITY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DATA_TABLE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS]]: true,

      // Performance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PERFORMANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CHARTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CARDS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DATA_TABLE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_ALERTS]]: false,

      // Safety
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SAFETY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_NOTIFICATIONS]]: true,
      // Safety Settings
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SETTINGS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_HAZARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_BARRIER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_LOTO]]: false,

      // Operations
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OPERATIONS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REPORTS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REGION_MAPPING]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_SERVICES]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CONTRACTS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS]]: false,

      // Admin
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.ADMIN]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_USERS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_EMAIL_LOG]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_ANALYTICS]]: false
    };

    this.storageService.set(AppConstants.userPermissions, analystPermission);
  }

  addManagerPermission(): void {
    const managerPermission = {
      // Site Info
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SITE_INFO]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_CUSTOMERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_PORTFOLIOS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_SITES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DEVICES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_EQUIPMENT]]: true,

      // Preventive Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SCOPE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_WORK_ORDERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SITE_AUDIT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE]]: true,

      // Corrective Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_ALL_TICKETS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_BILLING_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_MAP_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_RMA_REPORT]]: true,

      // Availability
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVAILABILITY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DATA_TABLE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS]]: true,

      // Performance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PERFORMANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CHARTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CARDS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DATA_TABLE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_ALERTS]]: true,

      // Safety
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SAFETY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_NOTIFICATIONS]]: true,
      // Safety Settings
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SETTINGS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_HAZARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_BARRIER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_LOTO]]: false,

      // Operations
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OPERATIONS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REGION_MAPPING]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_SERVICES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CONTRACTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS]]: true,

      // Admin
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.ADMIN]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_USERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_EMAIL_LOG]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_ANALYTICS]]: false
    };

    this.storageService.set(AppConstants.userPermissions, managerPermission);
  }

  addSupportPermission(): void {
    const supportPermission = {
      // Site Info
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SITE_INFO]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_CUSTOMERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_PORTFOLIOS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_SITES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DEVICES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_EQUIPMENT]]: true,

      // Preventive Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SCOPE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_WORK_ORDERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SITE_AUDIT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE]]: true,

      // Corrective Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_ALL_TICKETS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_BILLING_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_MAP_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_RMA_REPORT]]: true,

      // Availability
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVAILABILITY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DATA_TABLE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS]]: true,

      // Performance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PERFORMANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CHARTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CARDS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DATA_TABLE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_ALERTS]]: true,

      // Safety
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SAFETY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_NOTIFICATIONS]]: true,
      // Safety Settings
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SETTINGS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_HAZARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_BARRIER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_LOTO]]: false,

      // Operations
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OPERATIONS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REGION_MAPPING]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_SERVICES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CONTRACTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS]]: true,

      // Admin
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.ADMIN]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_USERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_EMAIL_LOG]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_ANALYTICS]]: false
    };

    this.storageService.set(AppConstants.userPermissions, supportPermission);
  }

  addFieldTechPermission(): void {
    const fieldTechPermission = {
      // Site Info
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SITE_INFO]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_CUSTOMERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_PORTFOLIOS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_SITES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DEVICES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_EQUIPMENT]]: true,

      // Preventive Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SCOPE]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_WORK_ORDERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SITE_AUDIT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE]]: false,

      // Corrective Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_ALL_TICKETS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_BILLING_REPORT]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_MAP_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_RMA_REPORT]]: true,

      // Availability
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVAILABILITY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DATA_TABLE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS]]: true,

      // Performance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PERFORMANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CHARTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CARDS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DATA_TABLE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_ALERTS]]: true,

      // Safety
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SAFETY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_NOTIFICATIONS]]: true,
      // Safety Settings
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SETTINGS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_HAZARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_BARRIER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_LOTO]]: false,

      // Operations
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OPERATIONS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REPORTS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REGION_MAPPING]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_SERVICES]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CONTRACTS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS]]: true,

      // Admin
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.ADMIN]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_USERS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_EMAIL_LOG]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_ANALYTICS]]: false
    };
    this.storageService.set(AppConstants.userPermissions, fieldTechPermission);
  }

  addCustomerPermission(): void {
    const customerPermission = {
      // Site Info
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SITE_INFO]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_CUSTOMERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_PORTFOLIOS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_SITES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DEVICES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_EQUIPMENT]]: false,

      // Preventive Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SCOPE]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_WORK_ORDERS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SITE_AUDIT]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE]]: false,

      // Corrective Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_ALL_TICKETS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_BILLING_REPORT]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_MAP_REPORT]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_RMA_REPORT]]: false,

      // Availability
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVAILABILITY]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_REPORTS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DATA_TABLE]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS]]: false,

      // Performance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PERFORMANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CHARTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CARDS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DATA_TABLE]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_ALERTS]]: false,

      // Safety
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SAFETY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_NOTIFICATIONS]]: false,
      // Safety Settings
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SETTINGS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_HAZARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_BARRIER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_LOTO]]: false,

      // Operations
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OPERATIONS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REPORTS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REGION_MAPPING]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_SERVICES]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CONTRACTS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS]]: false,

      // Admin
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.ADMIN]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_USERS]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_EMAIL_LOG]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_ANALYTICS]]: false
    };
    this.storageService.set(AppConstants.userPermissions, customerPermission);
  }

  addDirectorPermission(): void {
    const directorPermission = {
      // Site Info
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SITE_INFO]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_CUSTOMERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_PORTFOLIOS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_SITES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_DEVICES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SI_EQUIPMENT]]: true,

      // Preventive Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SCOPE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_WORK_ORDERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_SITE_AUDIT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE]]: true,

      // Corrective Maintenance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_ALL_TICKETS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_BILLING_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_MAP_REPORT]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.CM_RMA_REPORT]]: true,

      // Availability
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVAILABILITY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_DATA_TABLE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS]]: true,

      // Performance
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PERFORMANCE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DASHBOARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CHARTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CARDS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_DATA_TABLE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.PER_ALERTS]]: true,

      // Safety
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SAFETY]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_NOTIFICATIONS]]: true,
      // Safety Settings
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SETTINGS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_HAZARD]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_BARRIER]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.SF_SET_LOTO]]: true,

      // Operations
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OPERATIONS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REPORTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_REGION_MAPPING]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_SERVICES]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CONTRACTS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS]]: true,

      // Admin
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.ADMIN]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_USERS]]: true,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_EMAIL_LOG]]: false,
      [QE_MENU_MODULE_USER_PERMISSION_ENUM[QE_MENU_MODULE_ENUM.AD_ANALYTICS]]: true
    };
    this.storageService.set(AppConstants.userPermissions, directorPermission);
  }
}
