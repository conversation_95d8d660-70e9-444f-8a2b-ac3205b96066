export enum QE_MENU_MODULE_ENUM {
  SITE_INFO = 1, // Site Info
  PREVENTIVE_MAINTENANCE, // Preventive Maintenance
  CORRECTIVE_MAINTENANCE, // Corrective Maintenance
  AVAILABILITY, // Availability
  PERFORMANCE, // Performance
  SAFETY, // Safety
  OPERATIONS, // Operations
  ADMIN, // Admin
  OTHERS, // Placeholder for any future modules

  SI_DASHBOARD, // Site Info Dashboard
  SI_CUSTOMERS, // Site Info Customers
  SI_PORTFOLIOS, // Site Info Portfolios
  SI_SITES, // Site Info Sites
  SI_DEVICES, // Site Info Devices
  SI_EQUIPMENT, // Site Info Equipment

  PM_DASHBOARD, // Preventive Maintenance Dashboard
  PM_SCOPE, // Preventive Maintenance Scope
  PM_WORK_ORDERS, // Preventive Maintenance Work Orders
  PM_REPORTS, // Preventive Maintenance Reports
  PM_SITE_AUDIT, // Preventive Maintenance Site Audit
  PM_NON_CONFORMANCE, // Preventive Maintenance Non-Conformance

  CM_DASHBOARD, // Corrective Maintenance Dashboard
  CM_ALL_TICKETS, // Corrective Maintenance All Tickets
  CM_TICKET_AUDIT_REPORT, // Corrective Maintenance Ticket Audit Report
  CM_EXCLUSION_REPORT, // Corrective Maintenance Exclusion Report
  CM_BILLING_REPORT, // Corrective Maintenance Billing Report
  CM_TRUCK_ROLL_REPORT, // Corrective Maintenance Truck Roll Report
  CM_MAP_REPORT, // Corrective Maintenance Map Report
  CM_RMA_REPORT, // Corrective Maintenance RMA Report

  AVB_DASHBOARD, // Availability Dashboard
  AVB_REPORTS, // Availability Reports
  AVB_DATA_TABLE, // Availability Data Table
  AVB_EXCLUSIONS, // Availability Exclusions

  PER_DASHBOARD, // Performance Dashboard
  PER_POWER_CHARTS, // Performance Power Charts
  PER_POWER_CARDS, // Performance Power Cards
  PER_REPORTS, // Performance Reports
  PER_DATA_TABLE, // Performance Data Table
  PER_ALERTS, // Performance Alerts

  SF_JHA, // Safety JHA
  SF_SITE_CHECK_IN, // Safety Site Check In
  SF_SITE_AUDIT_JHA, // Safety Site Audit JHA
  SF_NOTIFICATIONS, // Safety Site Audit JHA Listing
  SF_SETTINGS, // Safety Settings
  SF_SET_GENERAL_INFO, // Safety Set General Info
  SF_SET_WORK_TYPE, // Safety Set Work Type
  SF_SET_WORK_STEP, // Safety Set Work Step
  SF_SET_HAZARD, // Safety Set Hazard
  SF_SET_BARRIER, // Safety Set Barrier
  SF_SET_JHA, // Safety Set JHA
  SF_SET_LOTO, // Safety Set LOTO

  OP_REPORTS, // Operations Reports
  OP_REGION_MAPPING, // Operations Region Mapping
  OP_SERVICES, // Operations Services
  OP_CONTRACTS, // Operations Contracts
  OP_CUSTOM_FORMS, // Operations Custom Forms

  AD_USERS, // Admin Users
  AD_DATA_SOURCES_MAPPING, // Admin Data Sources Mapping
  AD_API_ERROR_LOG, // Admin API Error Log
  AD_DEVICE_ERROR_LOG, // Admin API Usage Log
  AD_REPORT_SCHEDULER, // Admin Report Scheduler
  AD_CUSTOMER_API_GATEWAY, // Admin Customer API Gateway
  AD_API_GATEWAY_DASHBOARD, // Admin API Gateway Dashboard
  AD_RE_FETCH_SCHEDULER, // Admin Re-Fetch Scheduler
  AD_EMAIL_LOG, // Admin Email Log
  AD_ANALYTICS, // Admin Analytics

  SITE_CHECK_IN, // Site Check In
  USER_PROFILE, // User Profile,
  USER_CHANGE_PASSWORD, // User Change Password

  // Custom Menu
  SI_CUSTOM_SITES_SITE_DETAILS = 100001, // Site Info (Custom) -> Sites -> Site Details
  SI_CUSTOM_SITES_PERFORMANCE_INFORMATION = 100002, // Site Info (Custom) -> Sites -> Performance Information
  SI_CUSTOM_SITES_DEVICE_LIST = 100003 // Site Info (Custom) -> Sites -> Device List
}

export const QE_MENU_MODULE_NAME_ENUM = {
  // Site Info
  [QE_MENU_MODULE_ENUM.SITE_INFO]: 'Site Info',
  [QE_MENU_MODULE_ENUM.SI_DASHBOARD]: 'Dashboard',
  [QE_MENU_MODULE_ENUM.SI_CUSTOMERS]: 'Customers',
  [QE_MENU_MODULE_ENUM.SI_PORTFOLIOS]: 'Portfolios',
  [QE_MENU_MODULE_ENUM.SI_SITES]: 'Sites',
  [QE_MENU_MODULE_ENUM.SI_DEVICES]: 'Devices',
  [QE_MENU_MODULE_ENUM.SI_EQUIPMENT]: 'Equipment',

  // Preventive Maintenance
  [QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]: 'PM',
  [QE_MENU_MODULE_ENUM.PM_DASHBOARD]: 'Dashboard',
  [QE_MENU_MODULE_ENUM.PM_SCOPE]: 'Scope',
  [QE_MENU_MODULE_ENUM.PM_WORK_ORDERS]: 'Work Orders',
  [QE_MENU_MODULE_ENUM.PM_REPORTS]: 'Reports',
  [QE_MENU_MODULE_ENUM.PM_SITE_AUDIT]: 'Site Audit',
  [QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE]: 'Non-Conformance',

  // Corrective Maintenance
  [QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]: 'CM',
  [QE_MENU_MODULE_ENUM.CM_DASHBOARD]: 'Dashboard',
  [QE_MENU_MODULE_ENUM.CM_ALL_TICKETS]: 'All Tickets',
  [QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT]: 'Ticket Audit Report',
  [QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT]: 'Exclusion Report',
  [QE_MENU_MODULE_ENUM.CM_BILLING_REPORT]: 'Billing Report',
  [QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT]: 'Truck Roll Report',
  [QE_MENU_MODULE_ENUM.CM_MAP_REPORT]: 'Map Report',
  [QE_MENU_MODULE_ENUM.CM_RMA_REPORT]: 'RMA Report',

  // Availability
  [QE_MENU_MODULE_ENUM.AVAILABILITY]: 'Availability',
  [QE_MENU_MODULE_ENUM.AVB_DASHBOARD]: 'Dashboard',
  [QE_MENU_MODULE_ENUM.AVB_REPORTS]: 'Reports',
  [QE_MENU_MODULE_ENUM.AVB_DATA_TABLE]: 'Data Table',
  [QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS]: 'Exclusions',

  // Performance
  [QE_MENU_MODULE_ENUM.PERFORMANCE]: 'Performance',
  [QE_MENU_MODULE_ENUM.PER_DASHBOARD]: 'Dashboard',
  [QE_MENU_MODULE_ENUM.PER_POWER_CHARTS]: 'Power Charts',
  [QE_MENU_MODULE_ENUM.PER_POWER_CARDS]: 'Power Cards',
  [QE_MENU_MODULE_ENUM.PER_REPORTS]: 'Reports',
  [QE_MENU_MODULE_ENUM.PER_DATA_TABLE]: 'Data Table',
  [QE_MENU_MODULE_ENUM.PER_ALERTS]: 'Alerts',

  // Safety
  [QE_MENU_MODULE_ENUM.SAFETY]: 'Safety',
  [QE_MENU_MODULE_ENUM.SF_JHA]: 'JHA',
  [QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN]: 'Site Check-In',
  [QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA]: 'Site Audit-JHA',
  [QE_MENU_MODULE_ENUM.SF_NOTIFICATIONS]: 'Notifications',
  // Safety Settings
  [QE_MENU_MODULE_ENUM.SF_SETTINGS]: 'Settings',
  [QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO]: 'General Info',
  [QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE]: 'Work Type',
  [QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP]: 'Work Step',
  [QE_MENU_MODULE_ENUM.SF_SET_HAZARD]: 'Hazard',
  [QE_MENU_MODULE_ENUM.SF_SET_BARRIER]: 'Barrier',
  [QE_MENU_MODULE_ENUM.SF_SET_JHA]: 'JHA',
  [QE_MENU_MODULE_ENUM.SF_SET_LOTO]: 'LOTO',

  // Operations
  [QE_MENU_MODULE_ENUM.OPERATIONS]: 'Operations',
  [QE_MENU_MODULE_ENUM.OP_REPORTS]: 'Reports',
  [QE_MENU_MODULE_ENUM.OP_REGION_MAPPING]: 'Region Mapping',
  [QE_MENU_MODULE_ENUM.OP_SERVICES]: 'Services',
  [QE_MENU_MODULE_ENUM.OP_CONTRACTS]: 'Contracts',
  [QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS]: 'Custom Forms',

  // Admin
  [QE_MENU_MODULE_ENUM.ADMIN]: 'Admin',
  [QE_MENU_MODULE_ENUM.AD_USERS]: 'Users',
  [QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING]: 'Data Sources Mapping',
  [QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG]: 'API Error Log',
  [QE_MENU_MODULE_ENUM.AD_DEVICE_ERROR_LOG]: 'Device Error Log',
  [QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER]: 'Report Scheduler',
  [QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY]: 'Customer API Gateway',
  [QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD]: 'API Gateway Dashboard',
  [QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER]: 'Re-Fetch Scheduler',
  [QE_MENU_MODULE_ENUM.AD_EMAIL_LOG]: 'Email Log',
  [QE_MENU_MODULE_ENUM.AD_ANALYTICS]: 'Analytics',

  // Placeholder for any future modules
  [QE_MENU_MODULE_ENUM.OTHERS]: 'Others',
  [QE_MENU_MODULE_ENUM.SITE_CHECK_IN]: 'Site Check-In',
  [QE_MENU_MODULE_ENUM.USER_PROFILE]: 'User Profile',
  [QE_MENU_MODULE_ENUM.USER_CHANGE_PASSWORD]: 'Change Password',

  // Custom Menu
  [QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_SITE_DETAILS]: 'Site Details',
  [QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_PERFORMANCE_INFORMATION]: 'Performance Information',
  [QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_DEVICE_LIST]: 'Device List'
};

export const QE_MENU_MODULE_USER_PERMISSION_ENUM = {
  // Site Info
  [QE_MENU_MODULE_ENUM.SITE_INFO]: 'siteInfoPermission',
  [QE_MENU_MODULE_ENUM.SI_DASHBOARD]: 'siteInfoDashboardPermission',
  [QE_MENU_MODULE_ENUM.SI_CUSTOMERS]: 'siteInfoCustomersPermission',
  [QE_MENU_MODULE_ENUM.SI_PORTFOLIOS]: 'siteInfoPortfoliosPermission',
  [QE_MENU_MODULE_ENUM.SI_SITES]: 'siteInfoSitesPermission',
  [QE_MENU_MODULE_ENUM.SI_DEVICES]: 'siteInfoDevicesPermission',
  [QE_MENU_MODULE_ENUM.SI_EQUIPMENT]: 'siteInfoEquipmentPermission',

  // Preventive Maintenance
  [QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]: 'preventiveMaintenancePermission',
  [QE_MENU_MODULE_ENUM.PM_DASHBOARD]: 'preventiveMaintenanceDashboardPermission',
  [QE_MENU_MODULE_ENUM.PM_SCOPE]: 'preventiveMaintenanceScopePermission',
  [QE_MENU_MODULE_ENUM.PM_WORK_ORDERS]: 'preventiveMaintenanceWorkOrdersPermission',
  [QE_MENU_MODULE_ENUM.PM_REPORTS]: 'preventiveMaintenanceReportsPermission',
  [QE_MENU_MODULE_ENUM.PM_SITE_AUDIT]: 'preventiveMaintenanceSiteAuditPermission',
  [QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE]: 'preventiveMaintenanceNonConformancePermission',

  // Corrective Maintenance
  [QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]: 'correctiveMaintenancePermission',
  [QE_MENU_MODULE_ENUM.CM_DASHBOARD]: 'correctiveMaintenanceDashboardPermission',
  [QE_MENU_MODULE_ENUM.CM_ALL_TICKETS]: 'correctiveMaintenanceAllTicketsPermission',
  [QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT]: 'correctiveMaintenanceTicketAuditReportPermission',
  [QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT]: 'correctiveMaintenanceExclusionReportPermission',
  [QE_MENU_MODULE_ENUM.CM_BILLING_REPORT]: 'correctiveMaintenanceBillingReportPermission',
  [QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT]: 'correctiveMaintenanceTruckRollReportPermission',
  [QE_MENU_MODULE_ENUM.CM_MAP_REPORT]: 'correctiveMaintenanceMapReportPermission',
  [QE_MENU_MODULE_ENUM.CM_RMA_REPORT]: 'correctiveMaintenanceRMAReportPermission',

  // Availability
  [QE_MENU_MODULE_ENUM.AVAILABILITY]: 'availabilityPermission',
  [QE_MENU_MODULE_ENUM.AVB_DASHBOARD]: 'availabilityDashboardPermission',
  [QE_MENU_MODULE_ENUM.AVB_REPORTS]: 'availabilityReportsPermission',
  [QE_MENU_MODULE_ENUM.AVB_DATA_TABLE]: 'availabilityDataTablePermission',
  [QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS]: 'availabilityExclusionsPermission',

  // Performance
  [QE_MENU_MODULE_ENUM.PERFORMANCE]: 'performancePermission',
  [QE_MENU_MODULE_ENUM.PER_DASHBOARD]: 'performanceDashboardPermission',
  [QE_MENU_MODULE_ENUM.PER_POWER_CHARTS]: 'performancePowerChartsPermission',
  [QE_MENU_MODULE_ENUM.PER_POWER_CARDS]: 'performancePowerCardsPermission',
  [QE_MENU_MODULE_ENUM.PER_REPORTS]: 'performanceReportsPermission',
  [QE_MENU_MODULE_ENUM.PER_DATA_TABLE]: 'performanceDataTablePermission',
  [QE_MENU_MODULE_ENUM.PER_ALERTS]: 'performanceAlertsPermission',

  // Safety
  [QE_MENU_MODULE_ENUM.SAFETY]: 'safetyPermission',
  [QE_MENU_MODULE_ENUM.SF_JHA]: 'safetyJHAPermission',
  [QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN]: 'safetySiteCheckInPermission',
  [QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA]: 'safetySiteAuditJHAPermission',
  [QE_MENU_MODULE_ENUM.SF_NOTIFICATIONS]: 'safetyNotificationsPermission',
  // Safety Settings
  [QE_MENU_MODULE_ENUM.SF_SETTINGS]: 'safetySettingsPermission',
  [QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO]: 'safetySettingsGeneralInfoPermission',
  [QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE]: 'safetySettingsWorkTypePermission',
  [QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP]: 'safetySettingsWorkStepPermission',
  [QE_MENU_MODULE_ENUM.SF_SET_HAZARD]: 'safetySettingsHazardPermission',
  [QE_MENU_MODULE_ENUM.SF_SET_BARRIER]: 'safetySettingsBarrierPermission',
  [QE_MENU_MODULE_ENUM.SF_SET_JHA]: 'safetySettingsJHAPermission',
  [QE_MENU_MODULE_ENUM.SF_SET_LOTO]: 'safetySettingsLOTOPermission',

  // Operations
  [QE_MENU_MODULE_ENUM.OPERATIONS]: 'operationsPermission',
  [QE_MENU_MODULE_ENUM.OP_REPORTS]: 'operationsReportsPermission',
  [QE_MENU_MODULE_ENUM.OP_REGION_MAPPING]: 'operationsRegionMappingPermission',
  [QE_MENU_MODULE_ENUM.OP_SERVICES]: 'operationsServicesPermission',
  [QE_MENU_MODULE_ENUM.OP_CONTRACTS]: 'operationsContractsPermission',
  [QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS]: 'operationsCustomFormsPermission',

  // Admin
  [QE_MENU_MODULE_ENUM.ADMIN]: 'adminPermission',
  [QE_MENU_MODULE_ENUM.AD_USERS]: 'adminUsersPermission',
  [QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING]: 'adminDataSourcesMappingPermission',
  [QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG]: 'adminAPIErrorLogPermission',
  [QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER]: 'adminReportSchedulerPermission',
  [QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY]: 'adminCustomerAPIGatewayPermission',
  [QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD]: 'adminAPIGatewayDashboardPermission',
  [QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER]: 'adminReFetchSchedulerPermission',
  [QE_MENU_MODULE_ENUM.AD_EMAIL_LOG]: 'adminEmailLogPermission',
  [QE_MENU_MODULE_ENUM.AD_ANALYTICS]: 'adminAnalyticsPermission',

  // Placeholder for any future modules
  [QE_MENU_MODULE_ENUM.OTHERS]: 'othersPermission',
  [QE_MENU_MODULE_ENUM.SITE_CHECK_IN]: 'othersSiteCheckInPermission',
  [QE_MENU_MODULE_ENUM.USER_PROFILE]: 'othersUserProfilePermission',
  [QE_MENU_MODULE_ENUM.USER_CHANGE_PASSWORD]: 'othersChangePasswordPermission',

  // Custom Menu
  [QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_SITE_DETAILS]: 'customMenuSiteDetailsPermission',
  [QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_PERFORMANCE_INFORMATION]: 'customMenuPerformanceInformationPermission',
  [QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_DEVICE_LIST]: 'customMenuDeviceListPermission'
};

export const QE_MENU_MODULE_NAME_ENUM_LIST = {
  [QE_MENU_MODULE_ENUM.SITE_INFO]: {
    [QE_MENU_MODULE_ENUM.SI_DASHBOARD]: 'Dashboard',
    [QE_MENU_MODULE_ENUM.SI_CUSTOMERS]: 'Customers',
    [QE_MENU_MODULE_ENUM.SI_PORTFOLIOS]: 'Portfolios',
    [QE_MENU_MODULE_ENUM.SI_SITES]: 'Sites',
    [QE_MENU_MODULE_ENUM.SI_DEVICES]: 'Devices',
    [QE_MENU_MODULE_ENUM.SI_EQUIPMENT]: 'Equipment'
  },
  [QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]: {
    [QE_MENU_MODULE_ENUM.PM_DASHBOARD]: 'Dashboard',
    [QE_MENU_MODULE_ENUM.PM_SCOPE]: 'Scope',
    [QE_MENU_MODULE_ENUM.PM_WORK_ORDERS]: 'Work Orders',
    [QE_MENU_MODULE_ENUM.PM_REPORTS]: 'Reports',
    [QE_MENU_MODULE_ENUM.PM_SITE_AUDIT]: 'Site Audit',
    [QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE]: 'Non-Conformance'
  },
  [QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]: {
    [QE_MENU_MODULE_ENUM.CM_DASHBOARD]: 'Dashboard',
    [QE_MENU_MODULE_ENUM.CM_ALL_TICKETS]: 'All Tickets',
    [QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT]: 'Ticket Audit Report',
    [QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT]: 'Exclusion Report',
    [QE_MENU_MODULE_ENUM.CM_BILLING_REPORT]: 'Billing Report',
    [QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT]: 'Truck Roll Report',
    [QE_MENU_MODULE_ENUM.CM_MAP_REPORT]: 'Map Report',
    [QE_MENU_MODULE_ENUM.CM_RMA_REPORT]: 'RMA Report'
  },
  [QE_MENU_MODULE_ENUM.AVAILABILITY]: {
    [QE_MENU_MODULE_ENUM.AVB_DASHBOARD]: 'Dashboard',
    [QE_MENU_MODULE_ENUM.AVB_REPORTS]: 'Reports',
    [QE_MENU_MODULE_ENUM.AVB_DATA_TABLE]: 'Data Table',
    [QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS]: 'Exclusions'
  },
  [QE_MENU_MODULE_ENUM.PERFORMANCE]: {
    [QE_MENU_MODULE_ENUM.PER_DASHBOARD]: 'Dashboard',
    [QE_MENU_MODULE_ENUM.PER_POWER_CHARTS]: 'Power Charts',
    [QE_MENU_MODULE_ENUM.PER_POWER_CARDS]: 'Power Cards',
    [QE_MENU_MODULE_ENUM.PER_REPORTS]: 'Reports',
    [QE_MENU_MODULE_ENUM.PER_DATA_TABLE]: 'Data Table',
    [QE_MENU_MODULE_ENUM.PER_ALERTS]: 'Alerts'
  },
  [QE_MENU_MODULE_ENUM.SAFETY]: {
    [QE_MENU_MODULE_ENUM.SF_JHA]: 'JHA',
    [QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN]: 'Site Check-In',
    [QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA]: 'Site Audit-JHA',
    [QE_MENU_MODULE_ENUM.SF_NOTIFICATIONS]: 'Notifications',
    [QE_MENU_MODULE_ENUM.SF_SETTINGS]: 'Settings'
  },
  [QE_MENU_MODULE_ENUM.SF_SETTINGS]: {
    [QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO]: 'General Info',
    [QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE]: 'Work Type',
    [QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP]: 'Work Step',
    [QE_MENU_MODULE_ENUM.SF_SET_HAZARD]: 'Hazard',
    [QE_MENU_MODULE_ENUM.SF_SET_BARRIER]: 'Barrier',
    [QE_MENU_MODULE_ENUM.SF_SET_JHA]: 'JHA',
    [QE_MENU_MODULE_ENUM.SF_SET_LOTO]: 'LOTO'
  },
  [QE_MENU_MODULE_ENUM.OPERATIONS]: {
    [QE_MENU_MODULE_ENUM.OP_REPORTS]: 'Reports',
    [QE_MENU_MODULE_ENUM.OP_REGION_MAPPING]: 'Region Mapping',
    [QE_MENU_MODULE_ENUM.OP_SERVICES]: 'Services',
    [QE_MENU_MODULE_ENUM.OP_CONTRACTS]: 'Contracts',
    [QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS]: 'Custom Forms'
  },
  [QE_MENU_MODULE_ENUM.ADMIN]: {
    [QE_MENU_MODULE_ENUM.AD_USERS]: 'Users',
    [QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING]: 'Data Sources Mapping',
    [QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG]: 'API Error Log',
    [QE_MENU_MODULE_ENUM.AD_DEVICE_ERROR_LOG]: 'Device Usage Log',
    [QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER]: 'Report Scheduler',
    [QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY]: 'Customer API Gateway',
    [QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD]: 'API Gateway Dashboard',
    [QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER]: 'Re-Fetch Scheduler',
    [QE_MENU_MODULE_ENUM.AD_EMAIL_LOG]: 'Email Log',
    [QE_MENU_MODULE_ENUM.AD_ANALYTICS]: 'Analytics'
  },
  [QE_MENU_MODULE_ENUM.OTHERS]: {
    [QE_MENU_MODULE_ENUM.SITE_CHECK_IN]: 'Site Check-In',
    [QE_MENU_MODULE_ENUM.USER_PROFILE]: 'User Profile',
    [QE_MENU_MODULE_ENUM.USER_CHANGE_PASSWORD]: 'Change Password'
  }
};

export type FrontEndMenuOrderType = {
  [key: string]: number | FrontEndMenuOrderType;
};

export const QE_MENU_MODULE_OLD_USER_PERMISSION_LIST = [
  'User',
  'Site',
  'Portfolio',
  'Assessment',
  'WO',
  'Customer',
  'Report',
  'SiteAuditReport',
  'SiteDashboard',
  'PMDashboard',
  'CMDashboard',
  'AutomationDashboard',
  'Availability',
  'Equipments',
  'Tickets',
  'MyTickets',
  'CMReports',
  'AuditDispatchReport',
  'MapReport',
  'Exclusions',
  'Performance',
  'SiteDevice',
  'BillingReport',
  'TruckRollReport',
  'PerformanceReport',
  'RmaReport',
  'DataTable',
  'AvailabilityDataTable',
  'AvailabilityExclusion',
  'DataSource',
  'apiErrorLog',
  'AvailabilityReport',
  'PerformanceReports',
  'Safety',
  'Settings',
  'JHA',
  'GeneralInfo',
  'WorkType',
  'WorkStep',
  'Hazard',
  'Barrier',
  'LOTO',
  'PerformanceDashboard',
  'SiteCheckIn',
  'Notifications',
  'PerformancePowerChart',
  'PerformancePowerCards',
  'SiteAuditJHA',
  'PerformanceOutage',
  'reportSchedule',
  'CustomerAPIGateway',
  'APIGatewayDashboard',
  'EmailLog',
  'reFetchSchedule',
  'OperationsReports',
  'OperationsRegionMapping',
  'OperationsServices',
  'contracts',
  'CustomForms',
  'NonConformance',
  'Analytics'
];

export const QE_MENU_MODULE_ORDER_LIST: FrontEndMenuOrderType = {
  [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SITE_INFO]]: {
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SI_DASHBOARD]]: 1,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SI_CUSTOMERS]]: 2,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SI_PORTFOLIOS]]: 3,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SI_SITES]]: 4,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SI_DEVICES]]: 5,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SI_EQUIPMENT]]: 6
  },
  [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]]: {
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PM_DASHBOARD]]: 7,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PM_SCOPE]]: 8,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PM_WORK_ORDERS]]: 9,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PM_REPORTS]]: 10,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PM_SITE_AUDIT]]: 11,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE]]: 12
  },
  [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]]: {
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.CM_DASHBOARD]]: 13,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.CM_ALL_TICKETS]]: 14,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT]]: 15,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT]]: 16,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.CM_BILLING_REPORT]]: 17,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT]]: 18,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.CM_MAP_REPORT]]: 19,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.CM_RMA_REPORT]]: 20
  },
  [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AVAILABILITY]]: {
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AVB_DASHBOARD]]: 21,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AVB_REPORTS]]: 22,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AVB_DATA_TABLE]]: 23,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS]]: 24
  },
  [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PERFORMANCE]]: {
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PER_DASHBOARD]]: 25,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CHARTS]]: 26,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CARDS]]: 27,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PER_REPORTS]]: 28,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PER_DATA_TABLE]]: 29,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PER_ALERTS]]: 30
  },
  [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SAFETY]]: {
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_JHA]]: 31,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN]]: 32,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA]]: 33,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_NOTIFICATIONS]]: 34,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SETTINGS]]: {
      [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO]]: 35,
      [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE]]: 36,
      [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP]]: 37,
      [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SET_HAZARD]]: 38,
      [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SET_BARRIER]]: 39,
      [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SET_JHA]]: 40,
      [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SET_LOTO]]: 41
    }
  },
  [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.OPERATIONS]]: {
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.OP_REPORTS]]: 42,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.OP_REGION_MAPPING]]: 43,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.OP_SERVICES]]: 44,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.OP_CONTRACTS]]: 45,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS]]: 46
  },
  [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.ADMIN]]: {
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_USERS]]: 47,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING]]: 48,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG]]: 49,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_DEVICE_ERROR_LOG]]: 50,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER]]: 51,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY]]: 52,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD]]: 53,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER]]: 54,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_EMAIL_LOG]]: 55,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_ANALYTICS]]: 56
  },
  [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.OTHERS]]: {
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SITE_CHECK_IN]]: 57,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.USER_PROFILE]]: 58,
    [QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.USER_CHANGE_PASSWORD]]: 59
  }
};
