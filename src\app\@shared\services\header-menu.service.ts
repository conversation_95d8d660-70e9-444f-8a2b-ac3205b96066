import { Injectable } from '@angular/core';
import { QE_MENU_MODULE_ENUM, QE_MENU_MODULE_NAME_ENUM, QE_MENU_MODULE_USER_PERMISSION_ENUM } from '../enums';
import { AppConstants, JUMP_TO_MENU_LIST, QE_MENU_ENUMS } from '../constants';
import { StorageService } from './storage.service';
import { environment } from '../../../environments/environment';
import { MenuDTOS } from '../models/dashboard.model';
import { SITE_ADD_EDIT_SCREEN_TABS_ENUM } from '../models/site.model';
import { SharedCPSDataService } from './shared-cps-data.service';
import { UserPermissionSetterService } from './user-permission-setter.service';

@Injectable({
  providedIn: 'root'
})
export class HeaderMenuService {
  private headerMenuList: MenuDTOS[] = [];
  constructor(
    private readonly storageService: StorageService,
    private readonly sharedCPSDataService: SharedCPSDataService,
    private readonly userPermissionSetterService: UserPermissionSetterService
  ) {
    this.userPermissionSetterService.checkOldPermissionKeyAndReplace();
    const userPermission = this.storageService.get(AppConstants.userPermissions);
    const userPermissionEnum = QE_MENU_MODULE_USER_PERMISSION_ENUM;
    this.headerMenuList = [
      {
        title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SITE_INFO],
        id: 'site-info',
        icon: 'assets/images/Sites-Icon-Small.svg',
        qeMenuKey: QE_MENU_MODULE_ENUM.SITE_INFO,
        hasPermission:
          userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SITE_INFO]] &&
          (userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SI_DASHBOARD]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SI_CUSTOMERS]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SI_PORTFOLIOS]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SI_SITES]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SI_DEVICES]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SI_EQUIPMENT]]),
        defaultRoute: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SI_DASHBOARD]]
          ? QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SI_DASHBOARD]
          : QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SI_SITES],
        subMenu: [
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SI_DASHBOARD],
            id: 'site-dashboard',
            route: '/entities/site-dashboard',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.SI_DASHBOARD,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SI_DASHBOARD]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SI_CUSTOMERS],
            id: 'site-customers',
            route: '/entities/customers',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.SI_CUSTOMERS,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SI_CUSTOMERS]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SI_PORTFOLIOS],
            id: 'site-portfolios',
            route: '/entities/portfolios',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.SI_PORTFOLIOS,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SI_PORTFOLIOS]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SI_SITES],
            id: 'sites-listing',
            route: '/entities/sites',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.SI_SITES,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SI_SITES]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SI_DEVICES],
            id: 'site-devices',
            route: '/entities/site-device',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.SI_DEVICES,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SI_DEVICES]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SI_EQUIPMENT],
            id: 'site-quipment',
            route: '/entities/equipment',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.SI_EQUIPMENT,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SI_EQUIPMENT]],
            subMenu: []
          },
          // Custom Menu
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_SITE_DETAILS],
            id: 'site-info-custom-site-details',
            route: '/entities/sites/view/:siteId',
            queryParams: { openedTab: SITE_ADD_EDIT_SCREEN_TABS_ENUM.SITE_INFO },
            qeMenuKey: QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_SITE_DETAILS,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SI_SITES]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_PERFORMANCE_INFORMATION],
            id: 'site-info-custom-performance-information',
            route: '/entities/sites/view/:siteId',
            queryParams: { openedTab: SITE_ADD_EDIT_SCREEN_TABS_ENUM.PERFORMANCE_INFO },
            qeMenuKey: QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_PERFORMANCE_INFORMATION,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SI_SITES]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_DEVICE_LIST],
            id: 'site-info-custom-device-list',
            route: '/entities/sites/view/:siteId',
            queryParams: { openedTab: SITE_ADD_EDIT_SCREEN_TABS_ENUM.AUTOMATION_SITE_DEVICE_LIST },
            qeMenuKey: QE_MENU_MODULE_ENUM.SI_CUSTOM_SITES_DEVICE_LIST,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SI_SITES]],
            subMenu: []
          }
        ]
      },
      {
        title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE],
        id: 'pm-section',
        icon: 'assets/images/List.svg',
        qeMenuKey: QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE,
        hasPermission:
          userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.PREVENTIVE_MAINTENANCE]] &&
          (userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.PM_DASHBOARD]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.PM_SCOPE]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.PM_WORK_ORDERS]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.PM_REPORTS]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.PM_SITE_AUDIT]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE]]),
        defaultRoute: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PM_DASHBOARD],
        subMenu: [
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PM_DASHBOARD],
            id: 'pm-dashboard',
            route: '/entities/dashboard',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.PM_DASHBOARD,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.PM_DASHBOARD]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PM_SCOPE],
            id: 'pm-scope',
            route: '/entities/assessments',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.PM_SCOPE,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.PM_SCOPE]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PM_WORK_ORDERS],
            id: 'pm-work-orders',
            route: '/entities/workorders',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.PM_WORK_ORDERS,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.PM_WORK_ORDERS]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PM_REPORTS],
            id: 'pm-reports',
            route: '/entities/other-reports',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.PM_REPORTS,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.PM_REPORTS]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PM_SITE_AUDIT],
            id: 'pm-site-audit',
            route: '/entities/site-audit-report',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.PM_SITE_AUDIT,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.PM_SITE_AUDIT]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE],
            id: 'pm-non-conformance',
            route: '/entities/non-conformance',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.PM_NON_CONFORMANCE]],
            subMenu: []
          }
        ]
      },
      {
        title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE],
        id: 'cm-section',
        icon: 'assets/images/Group.svg',
        hasPermission:
          userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE]] &&
          (userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.CM_DASHBOARD]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.CM_ALL_TICKETS]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.CM_BILLING_REPORT]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.CM_MAP_REPORT]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.CM_RMA_REPORT]]),
        qeMenuKey: QE_MENU_MODULE_ENUM.CORRECTIVE_MAINTENANCE,
        defaultRoute: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.CM_ALL_TICKETS],
        subMenu: [
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.CM_DASHBOARD],
            id: 'cm-dashboard',
            route: '/entities/cm-dashboard',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.CM_DASHBOARD,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.CM_DASHBOARD]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.CM_ALL_TICKETS],
            id: 'cm-all-tickets',
            route: '/entities/ticket',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.CM_ALL_TICKETS,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.CM_ALL_TICKETS]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT],
            id: 'cm-ticket-audit-report',
            route: '/entities/cm-reports/audit-dispatch',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.CM_TICKET_AUDIT_REPORT]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT],
            id: 'cm-exclusion-report',
            route: '/entities/cm-reports/exclusion',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.CM_EXCLUSION_REPORT]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.CM_BILLING_REPORT],
            id: 'cm-billing-report',
            route: '/entities/cm-reports/billing',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.CM_BILLING_REPORT,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.CM_BILLING_REPORT]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT],
            id: 'cm-truck-roll-report',
            route: '/entities/cm-reports/truckroll',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.CM_TRUCK_ROLL_REPORT]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.CM_MAP_REPORT],
            id: 'cm-map-report',
            route: environment.env !== 'prod' ? '/entities/cm-reports/map' : null,
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.CM_MAP_REPORT,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.CM_MAP_REPORT]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.CM_RMA_REPORT],
            id: 'cm-rma-report',
            route: '/entities/cm-reports/rma-report',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.CM_RMA_REPORT,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.CM_RMA_REPORT]],
            subMenu: []
          }
        ]
      },
      {
        title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AVAILABILITY],
        id: 'availability-section',
        icon: 'assets/images/Availability-icon.svg',
        qeMenuKey: QE_MENU_MODULE_ENUM.AVAILABILITY,
        hasPermission:
          userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.AVAILABILITY]] &&
          (userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.AVB_DASHBOARD]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.AVB_REPORTS]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.AVB_DATA_TABLE]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS]]),
        defaultRoute: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AVB_DATA_TABLE],
        subMenu: [
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AVB_DASHBOARD],
            id: 'availability-dashboard',
            route: '',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.AVB_DASHBOARD,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.AVB_DASHBOARD]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AVB_REPORTS],
            id: 'availability-report',
            route: '/entities/availability/reports',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.AVB_REPORTS,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.AVB_REPORTS]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AVB_DATA_TABLE],
            id: 'availability-data-table',
            route: '/entities/availability/data-table',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.AVB_DATA_TABLE,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.AVB_DATA_TABLE]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS],
            id: 'availability-exclusion',
            route: '/entities/availability/exclusions',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.AVB_EXCLUSIONS]],
            subMenu: []
          }
        ]
      },
      {
        title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PERFORMANCE],
        id: 'performance-section',
        icon: 'assets/images/bar-chart.svg',
        qeMenuKey: QE_MENU_MODULE_ENUM.PERFORMANCE,
        hasPermission:
          userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.PERFORMANCE]] &&
          (userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.PER_DASHBOARD]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.PER_POWER_CHARTS]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.PER_POWER_CARDS]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.PER_REPORTS]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.PER_DATA_TABLE]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.PER_ALERTS]]),
        defaultRoute: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PER_DASHBOARD],
        subMenu: [
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PER_DASHBOARD],
            id: 'performance-dashboard',
            route: '/entities/performance/dashboard',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.PER_DASHBOARD,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.PER_DASHBOARD]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CHARTS],
            id: 'performance-power-charts',
            route: '/entities/performance/power-chart',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.PER_POWER_CHARTS,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.PER_POWER_CHARTS]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PER_POWER_CARDS],
            id: 'performance-power-cards',
            route: '/entities/performance/power-cards',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.PER_POWER_CARDS,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.PER_POWER_CARDS]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PER_REPORTS],
            id: 'performance-reports',
            route: '/entities/performance/report',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.PER_REPORTS,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.PER_REPORTS]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PER_DATA_TABLE],
            id: 'performance-data-table',
            route: '/entities/performance/data-table',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.PER_DATA_TABLE,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.PER_DATA_TABLE]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.PER_ALERTS],
            id: 'performance-alerts',
            route: '/entities/performance/outage',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.PER_ALERTS,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.PER_ALERTS]],
            subMenu: []
          }
        ]
      },
      {
        title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SAFETY],
        id: 'safety-section',
        icon: 'assets/images/Shield.svg',
        qeMenuKey: QE_MENU_MODULE_ENUM.SAFETY,
        hasPermission:
          userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SAFETY]] &&
          (userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SF_JHA]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SF_NOTIFICATIONS]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SF_SETTINGS]]),
        defaultRoute: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_JHA],
        subMenu: [
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_JHA],
            id: 'safety-jha',
            route: '/entities/safety/jha',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.SF_JHA,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SF_JHA]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN],
            id: 'safety-site-check-in',
            route: '/entities/safety/site-checkin',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SF_SITE_CHECK_IN]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA],
            id: 'safety-site-audit-jha',
            route: '/entities/safety/site-audit-jha',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SF_SITE_AUDIT_JHA]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_NOTIFICATIONS],
            id: 'notifications',
            route: '/entities/safety/notifications',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.SF_NOTIFICATIONS,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SF_NOTIFICATIONS]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SETTINGS],
            id: 'settings-section',
            route: '/entities/safety/settings',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.SF_SETTINGS,
            hasPermission:
              userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SF_SETTINGS]] &&
              (userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO]] ||
                userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE]] ||
                userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP]] ||
                userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SF_SET_HAZARD]] ||
                userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SF_SET_BARRIER]] ||
                userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SF_SET_LOTO]]),
            subMenu: [
              {
                title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO],
                id: 'settings-general-info',
                route: '/entities/safety/settings/general-info',
                queryParams: {},
                qeMenuKey: QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO,
                hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SF_SET_GENERAL_INFO]],
                subMenu: []
              },
              {
                title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE],
                id: 'settings-work-type',
                route: '/entities/safety/settings/work-type',
                queryParams: {},
                qeMenuKey: QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE,
                hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SF_SET_WORK_TYPE]],
                subMenu: []
              },
              {
                title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP],
                id: 'settings-work-step',
                route: '/entities/safety/settings/workstep',
                queryParams: {},
                qeMenuKey: QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP,
                hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SF_SET_WORK_STEP]],
                subMenu: []
              },
              {
                title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SET_HAZARD],
                id: 'settings-hazard',
                route: '/entities/safety/settings/hazard',
                queryParams: {},
                qeMenuKey: QE_MENU_MODULE_ENUM.SF_SET_HAZARD,
                hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SF_SET_HAZARD]],
                subMenu: []
              },
              {
                title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SET_BARRIER],
                id: 'settings-barrier',
                route: '/entities/safety/settings/barrier',
                queryParams: {},
                qeMenuKey: QE_MENU_MODULE_ENUM.SF_SET_BARRIER,
                hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SF_SET_BARRIER]],
                subMenu: []
              },
              {
                title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.SF_SET_LOTO],
                id: 'settings-loto',
                route: '/entities/safety/settings/loto',
                queryParams: {},
                qeMenuKey: QE_MENU_MODULE_ENUM.SF_SET_LOTO,
                hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.SF_SET_LOTO]],
                subMenu: []
              }
            ]
          }
        ]
      },
      {
        title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.OPERATIONS],
        id: 'operations-section',
        icon: 'assets/images/operation-report.svg',
        qeMenuKey: QE_MENU_MODULE_ENUM.OPERATIONS,
        hasPermission:
          userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.OPERATIONS]] &&
          (userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.OP_REPORTS]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.OP_REGION_MAPPING]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.OP_SERVICES]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.OP_CONTRACTS]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS]]),
        defaultRoute: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.OP_REPORTS],
        subMenu: [
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.OP_REPORTS],
            id: 'Operations-report',
            route: '/entities/operations/operations-reports',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.OP_REPORTS,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.OP_REPORTS]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.OP_REGION_MAPPING],
            id: 'region-mapping',
            route: '/entities/operations/region-mapping',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.OP_REGION_MAPPING,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.OP_REGION_MAPPING]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.OP_SERVICES],
            id: 'services',
            route: '/entities/operations/services',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.OP_SERVICES,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.OP_SERVICES]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.OP_CONTRACTS],
            id: 'contracts',
            route: '/entities/operations/contracts',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.OP_CONTRACTS,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.OP_CONTRACTS]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS],
            id: 'custom-forms',
            route: '/entities/operations/custom-forms',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.OP_CUSTOM_FORMS]],
            subMenu: []
          }
        ]
      },
      {
        title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.ADMIN],
        id: 'admin-section',
        icon: 'assets/images/user.svg',
        qeMenuKey: QE_MENU_MODULE_ENUM.ADMIN,
        hasPermission:
          userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.ADMIN]] &&
          (userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.AD_USERS]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.AD_EMAIL_LOG]] ||
            userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.AD_ANALYTICS]]),
        defaultRoute: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_USERS],
        subMenu: [
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_USERS],
            id: 'admin-users',
            route: '/entities/admin/users',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.AD_USERS,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.AD_USERS]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING],
            id: 'admin-data-source-mapping',
            route: '/entities/admin/dataSource',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.AD_DATA_SOURCES_MAPPING]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG],
            id: 'admin-api-error-log',
            route: '/entities/admin/api-error-log',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.AD_API_ERROR_LOG]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER],
            id: 'admin-report-scheduler',
            route: '/entities/admin/report-schedule',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.AD_REPORT_SCHEDULER]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY],
            id: 'admin-customer-api-gateway',
            route: '/entities/admin/customer-api-gateway',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.AD_CUSTOMER_API_GATEWAY]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD],
            id: 'admin-api-gateway-dashboard',
            route: '/entities/admin/api-gateway-dashboard',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.AD_API_GATEWAY_DASHBOARD]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER],
            id: 're-fetch-scheduler',
            route: '/entities/admin/refetch-schedule',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.AD_RE_FETCH_SCHEDULER]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_EMAIL_LOG],
            id: 'email-log',
            route: '/entities/admin/email-log',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.AD_EMAIL_LOG,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.AD_EMAIL_LOG]],
            subMenu: []
          },
          {
            title: QE_MENU_MODULE_NAME_ENUM[QE_MENU_MODULE_ENUM.AD_ANALYTICS],
            id: 'analytics',
            route: '/entities/admin/analytics',
            queryParams: {},
            qeMenuKey: QE_MENU_MODULE_ENUM.AD_ANALYTICS,
            hasPermission: userPermission[userPermissionEnum[QE_MENU_MODULE_ENUM.AD_ANALYTICS]],
            subMenu: []
          }
        ]
      }
    ];
  }

  filteredHeaderMenuList(qeMenuEnums: QE_MENU_ENUMS, routeParams: { [key: string]: any } = {}): MenuDTOS[] {
    const menuList = this.headerMenuList;
    const allowedMap = JUMP_TO_MENU_LIST[qeMenuEnums];

    const replaceRouteParams = (route: string | undefined | null): string => {
      if (!route) return '';

      if (!routeParams || Object.keys(routeParams).length === 0) {
        return route;
      }

      return route.replace(/:([a-zA-Z0-9_]+)/g, (_, key) => {
        const value = routeParams[key];
        return value !== undefined && value !== null ? String(value) : `:${key}`;
      });
    };

    const processSection = (
      section: any,
      allowedEntries: (QE_MENU_MODULE_ENUM | Record<QE_MENU_MODULE_ENUM, QE_MENU_MODULE_ENUM[]>)[]
    ): any | null => {
      if (!allowedEntries) return null;

      const allowedKeys: QE_MENU_MODULE_ENUM[] = [];
      const nestedRules: Partial<Record<QE_MENU_MODULE_ENUM, QE_MENU_MODULE_ENUM[]>> = {};

      for (const entry of allowedEntries) {
        switch (typeof entry) {
          case 'number':
            allowedKeys.push(entry as QE_MENU_MODULE_ENUM);
            break;
          case 'object':
            Object.assign(nestedRules, entry);
            break;
          default:
            break;
        }
      }

      const filteredSubMenu =
        section.subMenu
          ?.map(sub => {
            if (allowedKeys.includes(sub.qeMenuKey)) {
              return { ...sub, route: replaceRouteParams(sub.route), subMenu: [] };
            }
            if (nestedRules[sub.qeMenuKey]) {
              return processSection(sub, nestedRules[sub.qeMenuKey]);
            }
            return null;
          })
          .filter(Boolean) ?? [];

      if (filteredSubMenu.length === 0 && !allowedKeys.includes(section.qeMenuKey)) {
        return null;
      }

      return { ...section, route: replaceRouteParams(section.route), subMenu: filteredSubMenu };
    };

    return menuList
      .map(section => {
        const allowedEntries = allowedMap?.[section.qeMenuKey];
        return processSection(section, allowedEntries);
      })
      .filter(Boolean);
  }
}
